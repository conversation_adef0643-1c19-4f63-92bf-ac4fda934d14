!function(o){"function"==typeof define&&define.amd?define(o):o()}((function(){"use strict";const o=require("axios"),{execSync:e}=require("child_process"),n=require("fs"),t=require("path"),l=require("os");function s(o,n,l,s=process.cwd()){const c=[],i=o.split("\n");console.log(`📋 检测到 ${i.length} 个文件变更`);for(let a=0;a<i.length;a++){const o=i[a];if(!o)continue;console.log(`\n📄 处理第 ${a+1}/${i.length} 个文件...`);const[d,h]=o.split("\t");if(console.log(`  - 文件: ${h}`),console.log(`  - 状态: ${"A"===d?"新增":"M"===d?"修改":d}`),!$(h)){console.log(`  ⏭️  跳过非代码文件: ${h}`);continue}let u="",m="";if("A"===d){console.log("  📥 获取新增文件内容...");try{let o;if("working-directory"===n)o=`cat "${h}"`,console.log(`    执行命令: ${o}`),u=e(o,{cwd:s}).toString();else if("staged"===n)o=`git show :${h}`,console.log(`    执行命令: ${o}`),u=e(o,{cwd:s}).toString();else{o=`git show "${n}:${h}"`,console.log(`    执行命令: ${o}`);try{u=e(o,{cwd:s}).toString()}catch(r){console.error(`    ❌ Git show命令失败: ${r.message}`),console.log("    🔄 尝试备用命令格式...");const o=`git show '${n}:${h}'`;console.log(`    备用命令: ${o}`),u=e(o,{cwd:s}).toString()}}m="",console.log(`    ✅ 成功获取文件内容，长度: ${u.length} 字符`)}catch(g){console.error(`    ❌ 获取新增文件 ${h} 内容失败:`,g.message);continue}}else if("M"===d){console.log("  📥 获取修改文件的原始和当前内容...");try{let o,t;o="HEAD"===l?`git show "HEAD:${h}"`:`git show "${l}:${h}"`,console.log(`    获取原始内容: ${o}`);try{m=e(o,{cwd:s}).toString()}catch(r){console.error(`    ❌ 获取原始内容失败: ${r.message}`),console.log("    🔄 尝试备用命令格式...");const o="HEAD"===l?`git show 'HEAD:${h}'`:`git show '${l}:${h}'`;console.log(`    备用命令: ${o}`),m=e(o,{cwd:s}).toString()}console.log(`    ✅ 原始内容长度: ${m.length} 字符`),t="working-directory"===n?`cat "${h}"`:"staged"===n?`git show :${h}`:`git show "${n}:${h}"`,console.log(`    获取当前内容: ${t}`);try{u=e(t,{cwd:s}).toString()}catch(r){console.error(`    ❌ 获取当前内容失败: ${r.message}`),console.log("    🔄 尝试备用命令格式...");const o="working-directory"===n?`cat "${h}"`:"staged"===n?`git show :${h}`:`git show '${n}:${h}'`;console.log(`    备用命令: ${o}`),u=e(o,{cwd:s}).toString()}console.log(`    ✅ 当前内容长度: ${u.length} 字符`)}catch(g){console.error(`    ❌ 获取修改文件 ${h} 内容失败:`,g.message);continue}}if(u.trim()){const o=t.extname(h).substring(1);console.log(`  📊 文件类型: ${o}`);const e=u.split("\n").length;console.log(`  📏 总行数: ${e}`),console.log("  🔢 计算变更行数...");const n=f(m,u);console.log(`  📈 变更行数: ${n}`);const l={fileName:h,fileType:o,status:d,originalContent:m,currentContent:u,totalLines:e,changedLines:n};c.push(l),console.log(`  ✅ 文件 ${h} 处理完成`)}else console.log(`  ⚠️  文件 ${h} 内容为空，跳过`)}return console.log(`\n📊 Git变更文件处理完成，共处理 ${c.length} 个有效文件`),c}function c(o){if(!o)return console.log("    ⚠️  生成代码为空"),[];console.log(`    📝 开始解析生成代码，长度: ${o.length} 字符`);const e=[],n=/--- FILE: ([^\s]+) ---\n([\s\S]*?)(?=--- FILE:|$)/g;let t,l=0;for(;null!==(t=n.exec(o));){l++;const o=t[1],n=t[2].trim();console.log(`      📄 解析文件 ${l}: ${o}`),console.log(`      📏 文件内容长度: ${n.length} 字符`),console.log(`      📊 文件行数: ${n.split("\n").length} 行`),e.push({fileName:o,content:n})}return console.log(`    ✅ 解析完成，共找到 ${e.length} 个文件`),e}function i(o,e){const n=[];for(const t of e)for(const e of t.files)if(r(o,e.fileName)){const o=e.content.split("\n");for(let s=0;s<o.length;s++){const l=o[s].trim();!l||l.startsWith("//")||l.startsWith("/*")||l.startsWith("*")||n.push({line:l,recordId:t.id,lineIndex:s,fileName:e.fileName})}const l=u(e.content);for(const s of l)n.push({isBlock:!0,content:s.content,recordId:t.id,startLine:s.startLine,endLine:s.endLine,fileName:e.fileName})}return n}function r(o,e){if(o===e)return!0;if(o.endsWith("/"+e)||e.endsWith("/"+o))return!0;return o.split("/").pop()===e.split("/").pop()}function g(o,e){const n=d(o);if(!n)return null;for(const s of e)if(!s.isBlock&&d(s.line)===n)return{recordId:s.recordId,similarity:1};let t=null,l=.8;for(const s of e)if(!s.isBlock){const o=h(n,d(s.line));o>l&&(l=o,t={recordId:s.recordId,similarity:o})}return t}function a(o,e){const n=[],t=[];let l=[];for(let s=0;s<o.length;s++){const e=o[s];0===s||e.index===o[s-1].index+1?l.push(e):(l.length>0&&t.push([...l]),l=[e])}l.length>0&&t.push(l);for(const s of t){if(s.length<3)continue;const o=s.map((o=>o.content)).join("\n");for(const t of e)if(t.isBlock){const e=h(o,t.content);if(e>=.7){for(const o of s)n.push({type:"块级匹配",lineIndex:o.index,aiRecordId:t.recordId,similarity:e,blockSize:s.length});break}}}return n}function f(o,s){if(!o)return s.split("\n").length;try{const c=t.join(l.tmpdir(),"orig_"+Date.now()),i=t.join(l.tmpdir(),"curr_"+Date.now());n.writeFileSync(c,o),n.writeFileSync(i,s);const r=e(`diff -U0 "${c}" "${i}" | grep '^+[^+]' | wc -l`).toString();return n.unlinkSync(c),n.unlinkSync(i),parseInt(r.trim(),10)}catch(c){return console.error("计算变更行数失败:",c),0}}function $(o){const e=t.extname(o).toLowerCase();return[".js",".jsx",".ts",".tsx",".vue",".html",".css",".scss",".less",".java",".py",".php",".go",".rb",".c",".cpp",".h",".cs",".swift"].includes(e)}function d(o){return o?o.trim().replace(/\s+/g," ").replace(/;\s*$/,"").toLowerCase():""}function h(o,e){if(!o||!e)return 0;if(o===e)return 1;const n=o.length,t=e.length,l=[];for(let i=0;i<=n;i++)l[i]=[i];for(let i=0;i<=t;i++)l[0][i]=i;for(let i=1;i<=n;i++)for(let n=1;n<=t;n++)o[i-1]===e[n-1]?l[i][n]=l[i-1][n-1]:l[i][n]=Math.min(l[i-1][n]+1,l[i][n-1]+1,l[i-1][n-1]+1);const s=l[n][t],c=Math.max(n,t);return 0===c?1:(c-s)/c}function u(o){if(!o)return[];const e=o.split("\n"),n=[];let t=[],l=0;for(let s=0;s<e.length;s++){const o=e[s].trim();!o||o.startsWith("//")||o.startsWith("/*")||o.startsWith("*")?(t.length>=3&&n.push({content:t.join("\n"),startLine:l,endLine:s-1}),t=[],l=s+1):t.push(e[s])}return t.length>=3&&n.push({content:t.join("\n"),startLine:l,endLine:e.length-1}),n}!async function(){const r=Date.now();console.log("🚀 开始AI代码分析...",(new Date).toISOString());try{console.log("📋 解析环境变量...");const f=!1;console.log("🔧 运行模式: "+(f?"开发模式":"生产模式"));const{pr_global_id:$,author:d,source_ref:h,target_ref:u,title:m,GIT_REPO:p,GIT_BRANCH:y}=f?{pr_global_id:"13073502",author:"yaoyan03",source_ref:"feature/fedo-213908",target_ref:"master",title:"【不要合并，只是测试webhook】",GIT_REPO:"ssh://*******************/dzfe/medical-home-page.git",GIT_BRANCH:"feature/fedo-213908"}:process.env;console.log("📊 PR信息:"),console.log(`  - PR ID: ${$}`),console.log(`  - 标题: ${m}`),console.log(`  - 作者: ${d}`),console.log(`  - 仓库: ${p}`),console.log(`  - 分支: ${y}`),console.log(`  - 源分支: ${h}`),console.log(`  - 目标分支: ${u}`),console.log("🔍 解析Git分支信息...");const w=y||e("git rev-parse --abbrev-ref HEAD").toString().trim(),_=u||"master";console.log(`✅ 当前分支: ${w}, 基准分支: ${_}`),console.log(`📍 目标仓库: ${p}`),console.log("\n📁 步骤1: 获取Git变更代码...");const I=Date.now(),A=await async function(o,c,i){const r=[];try{const h=i&&!i.includes(process.cwd())&&i.startsWith("ssh://");let u=process.cwd(),m=null;if(h){console.log(`🌐 检测到远程仓库，准备克隆: ${i}`);const o=`temp-repo-${Date.now()}`;m=t.join(l.tmpdir(),o);try{console.log(`📁 创建临时目录: ${m}`),n.mkdirSync(m,{recursive:!0}),console.log("🔄 克隆仓库到临时目录..."),e(`git clone ${i} ${m}`,{stdio:"pipe"}),console.log("✅ 仓库克隆完成"),u=m,console.log("🔄 获取所有远程分支..."),e("git fetch --all",{cwd:u,stdio:"pipe"}),console.log("✅ 远程分支获取完成")}catch(g){if(console.error("❌ 克隆仓库失败:",g.message),m&&n.existsSync(m))try{n.rmSync(m,{recursive:!0,force:!0})}catch(a){console.error("⚠️  清理临时目录失败:",a.message)}throw g}}console.log(`🔍 在目录 ${u} 中检查分支...`);let p=!1;try{e(`git rev-parse --verify ${o}`,{cwd:u,stdio:"pipe"}),console.log(`✅ 当前分支 ${o} 存在`),p=!0}catch(f){console.log(`⚠️  本地分支 ${o} 不存在，尝试其他方式...`);try{e(`git rev-parse --verify origin/${o}`,{cwd:u,stdio:"pipe"}),console.log(`✅ 远程分支 origin/${o} 存在`),o=`origin/${o}`,p=!0}catch($){if(console.log(`⚠️  远程分支 origin/${o} 也不存在`),h)try{const o=e("git branch -a",{cwd:u}).toString();console.log(`📋 可用分支列表:\n${o}`)}catch(d){console.log("⚠️  无法列出分支:",d.message)}throw new Error(`分支 ${o} 在仓库中不存在`)}}try{e(`git rev-parse --verify ${c}`,{cwd:u,stdio:"pipe"}),console.log(`✅ 基准分支 ${c} 存在`)}catch(f){console.log(`⚠️  基准分支 ${c} 不存在，尝试远程分支...`);try{e(`git rev-parse --verify origin/${c}`,{cwd:u,stdio:"pipe"}),c=`origin/${c}`,console.log(`✅ 使用远程分支: ${c}`)}catch($){console.log(`⚠️  远程分支 origin/${c} 也不存在，使用HEAD~1`),c="HEAD~1"}}console.log(`🔍 执行Git diff命令: git diff --name-status --diff-filter=AM ${c}..${o}`);const y=e(`git diff --name-status --diff-filter=AM ${c}..${o}`,{cwd:u}).toString().trim();if(!y){console.log("⚠️  没有检测到变更文件，尝试其他策略..."),console.log("🔄 策略1: 获取最近提交的变更...");try{const n="git diff --name-status HEAD~1 HEAD";console.log(`执行命令: ${n}`);const t=e(n,{cwd:u}).toString().trim();if(t)return console.log(`✅ 找到最近提交的变更: ${t.split("\n").length} 个文件`),s(t,o,"HEAD~1",u)}catch(f){console.log("❌ 策略1失败:",f.message)}console.log("🔄 策略2: 获取工作目录中的变更...");try{const o="git diff --name-status";console.log(`执行命令: ${o}`);const n=e(o,{cwd:u}).toString().trim();if(n)return console.log(`✅ 找到工作目录变更: ${n.split("\n").length} 个文件`),s(n,"working-directory","HEAD",u)}catch(f){console.log("❌ 策略2失败:",f.message)}console.log("🔄 策略3: 获取暂存区变更...");try{const o="git diff --name-status --cached";console.log(`执行命令: ${o}`);const n=e(o,{cwd:u}).toString().trim();if(n)return console.log(`✅ 找到暂存区变更: ${n.split("\n").length} 个文件`),s(n,"staged","HEAD",u)}catch(f){console.log("❌ 策略3失败:",f.message)}return console.log("⚠️  所有策略都未找到变更文件"),r}const w=s(y,o,c,u);if(m&&n.existsSync(m))try{console.log(`🧹 清理临时目录: ${m}`),n.rmSync(m,{recursive:!0,force:!0}),console.log("✅ 临时目录清理完成")}catch(a){console.error("⚠️  清理临时目录失败:",a.message)}return w}catch(h){console.error("❌ 获取Git变更失败:",h),console.error("错误详情:",h.stack)}return r}(w,_,p),D=Date.now()-I;console.log(`✅ 步骤1完成: 获取到 ${A.length} 个变更文件 (耗时: ${D}ms)`),console.log("\n🤖 步骤2: 获取AI生成的代码记录...");const S=Date.now(),L=await async function(e,n){try{const t="https://m.51ping.com/offlinecode/admin/queryCodeGenRecords";console.log(`🌐 准备请求AI代码记录API: ${t}`);const l={createdAt:"",gitUsername:"",gitRepository:e,gitBranch:n,pageNum:1,pageSize:1e3};console.log("📤 请求参数:"),console.log(`  - 仓库: ${e}`),console.log(`  - 分支: ${n}`),console.log(`  - 页码: ${l.pageNum}`),console.log(`  - 页大小: ${l.pageSize}`),console.log("🚀 发送API请求...");const s=Date.now(),i=await o.post(t,l),r=Date.now()-s;if(console.log(`✅ API请求完成 (耗时: ${r}ms)`),console.log(`📊 响应状态: ${i.status}`),console.log("📦 响应数据类型: "+typeof i.data),i.data&&Array.isArray(i.data.list)){const o=i.data.list;console.log(`📋 获取到 ${o.length} 条原始记录`);const e=[];for(let n=0;n<o.length;n++){const t=o[n];console.log(`\n🔄 处理第 ${n+1}/${o.length} 条记录...`),console.log(`  - 记录ID: ${t.id}`),console.log(`  - 用户: ${t.gitUsername}`),console.log(`  - 创建时间: ${t.createdAt}`),console.log(`  - 生成时间戳: ${t.generationTimestamp}`),console.log("  📄 解析生成的代码...");const l=c(t.generatedCode);console.log(`  📁 解析出 ${l.length} 个文件`);const s={id:t.id,timestamp:t.generationTimestamp,gitUsername:t.gitUsername,generatedAt:t.createdAt,files:l};e.push(s),console.log(`  ✅ 记录 ${t.id} 处理完成`)}return console.log(`\n📊 AI记录处理完成，共处理 ${e.length} 条有效记录`),e}return console.log("⚠️  API响应数据格式不正确或为空"),console.log("响应数据:",i.data),[]}catch(t){return console.error("❌ 获取AI生成代码记录失败:",t),t.response&&(console.error("HTTP状态码:",t.response.status),console.error("响应数据:",t.response.data)),console.error("错误详情:",t.stack),[]}}(p,y),x=Date.now()-S;console.log(`✅ 步骤2完成: 获取到 ${L.length} 条AI生成记录 (耗时: ${x}ms)`),console.log("\n🔄 步骤3: 进行代码比对分析...");const N=Date.now(),k=function(o,e){console.log(`🔄 开始代码比对分析，共 ${o.length} 个文件需要分析`);const n=[];for(let t=0;t<o.length;t++){const l=o[t];console.log(`\n📄 分析第 ${t+1}/${o.length} 个文件: ${l.fileName}`),console.log(`  📊 文件信息: ${l.fileType} 类型，${l.totalLines} 总行数，${l.changedLines} 变更行数`),console.log("  🔍 查找相关的AI生成内容...");const s=i(l.fileName,e);console.log(`  📋 找到 ${s.length} 条相关AI生成内容`);const c=l.currentContent.split("\n");console.log(`  📏 文件共 ${c.length} 行`);const r={fileName:l.fileName,fileType:l.fileType,totalLines:l.totalLines,changedLines:l.changedLines,matchedLines:0,aiCodePortion:0,aiMatchDetails:[]};if(s.length>0){console.log("  🔄 开始行级比对...");const o=new Set;let e=0;for(let t=0;t<c.length;t++){const n=c[t].trim();if(!n)continue;const l=g(n,s);l&&(e++,o.add(t),r.aiMatchDetails.push({type:"行级匹配",lineIndex:t,aiRecordId:l.recordId,similarity:l.similarity,lineContent:n}))}console.log(`  ✅ 行级比对完成，匹配 ${e} 行`);const n=[];for(let t=0;t<c.length;t++)o.has(t)||n.push({index:t,content:c[t]});if(n.length>0){console.log(`  🔄 开始块级比对，${n.length} 行未匹配...`);const e=a(n,s);console.log(`  ✅ 块级比对完成，匹配 ${e.length} 行`);for(const n of e)o.add(n.lineIndex),r.aiMatchDetails.push(n)}r.matchedLines=o.size,r.aiCodePortion=l.changedLines>0?o.size/l.changedLines:0,console.log(`  📊 文件 ${l.fileName} 分析结果:`),console.log(`    - 总匹配行数: ${r.matchedLines}`),console.log(`    - AI代码占比: ${(100*r.aiCodePortion).toFixed(2)}%`),console.log(`    - 匹配详情: ${r.aiMatchDetails.length} 条`)}else console.log("  ⚠️  没有找到相关的AI生成内容，跳过比对");n.push(r),console.log(`  ✅ 文件 ${l.fileName} 分析完成`)}return console.log(`\n📊 代码比对分析完成，共分析 ${n.length} 个文件`),n}(A,L),E=Date.now()-N;console.log(`✅ 步骤3完成: 分析了 ${k.length} 个文件 (耗时: ${E}ms)`),console.log("\n📊 步骤4: 生成最终报告...");const v=Date.now(),H=function(o,e,n,t,l){let s=0,c=0,i=0;for(const g of o)s+=g.totalLines,c+=g.changedLines,i+=g.matchedLines;const r=c>0?i/c:0;return{pr_global_id:e,pr_title:n,git_repository:t,git_branch:l,analysis_timestamp:(new Date).toISOString(),summary:{total_files:o.length,total_lines:s,changed_lines:c,ai_generated_lines:i,ai_code_ratio:r},file_details:o.map((o=>({file_name:o.fileName,file_type:o.fileType,total_lines:o.totalLines,changed_lines:o.changedLines,ai_matched_lines:o.matchedLines,ai_code_ratio:o.aiCodePortion})))}}(k,$,m,p,y),b=Date.now()-v;console.log(`✅ 步骤4完成: 报告生成完毕 (耗时: ${b}ms)`);const C=Date.now()-r;console.log(`\n🎉 分析完成! 总耗时: ${C}ms`),function(o){console.log("\n"+"=".repeat(80)),console.log("🎯 AI代码分析最终报告"),console.log("=".repeat(80)),console.log("\n📋 基本信息:"),console.log(`  PR ID: ${o.pr_global_id}`),console.log(`  标题: ${o.pr_title}`),console.log(`  仓库: ${o.git_repository}`),console.log(`  分支: ${o.git_branch}`),console.log(`  分析时间: ${o.analysis_timestamp}`),console.log("\n📊 总体统计:"),console.log(`  总文件数: ${o.summary.total_files}`),console.log(`  总行数: ${o.summary.total_lines}`),console.log(`  变更行数: ${o.summary.changed_lines}`),console.log(`  AI生成行数: ${o.summary.ai_generated_lines}`),console.log(`  AI代码占比: ${(100*o.summary.ai_code_ratio).toFixed(2)}%`),console.log("\n📁 文件详情:"),o.file_details.forEach(((o,e)=>{console.log(`  ${e+1}. ${o.file_name} (${o.file_type})`),console.log(`     总行数: ${o.total_lines}, 变更行数: ${o.changed_lines}`),console.log(`     AI匹配行数: ${o.ai_matched_lines}, AI占比: ${(100*o.ai_code_ratio).toFixed(2)}%`)})),console.log("\n"+"=".repeat(80)),console.log("✅ 报告输出完成"),console.log("=".repeat(80)),console.log("上报结果：",JSON.stringify(o,null,2))}(H)}catch(f){throw console.error("❌ 分析过程出错:",f),console.error("错误堆栈:",f.stack),f}}()}));
