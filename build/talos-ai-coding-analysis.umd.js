!function(o){"function"==typeof define&&define.amd?define(o):o()}((function(){"use strict";const o=require("axios"),{execSync:e}=require("child_process"),n=require("fs"),t=require("path"),l=require("os");function s(o){if(!o)return console.log("    ⚠️  生成代码为空"),[];console.log(`    📝 开始解析生成代码，长度: ${o.length} 字符`);const e=[],n=/--- FILE: ([^\s]+) ---\n([\s\S]*?)(?=--- FILE:|$)/g;let t,l=0;for(;null!==(t=n.exec(o));){l++;const o=t[1],n=t[2].trim();console.log(`      📄 解析文件 ${l}: ${o}`),console.log(`      📏 文件内容长度: ${n.length} 字符`),console.log(`      📊 文件行数: ${n.split("\n").length} 行`),e.push({fileName:o,content:n})}return console.log(`    ✅ 解析完成，共找到 ${e.length} 个文件`),e}function i(o,e){const n=[];for(const t of e)for(const e of t.files)if(c(o,e.fileName)){const o=e.content.split("\n");for(let s=0;s<o.length;s++){const l=o[s].trim();!l||l.startsWith("//")||l.startsWith("/*")||l.startsWith("*")||n.push({line:l,recordId:t.id,lineIndex:s,fileName:e.fileName})}const l=$(e.content);for(const s of l)n.push({isBlock:!0,content:s.content,recordId:t.id,startLine:s.startLine,endLine:s.endLine,fileName:e.fileName})}return n}function c(o,e){if(o===e)return!0;if(o.endsWith("/"+e)||e.endsWith("/"+o))return!0;return o.split("/").pop()===e.split("/").pop()}function r(o,e){const n=h(o);if(!n)return null;for(const s of e)if(!s.isBlock&&h(s.line)===n)return{recordId:s.recordId,similarity:1};let t=null,l=.8;for(const s of e)if(!s.isBlock){const o=d(n,h(s.line));o>l&&(l=o,t={recordId:s.recordId,similarity:o})}return t}function a(o,e){const n=[],t=[];let l=[];for(let s=0;s<o.length;s++){const e=o[s];0===s||e.index===o[s-1].index+1?l.push(e):(l.length>0&&t.push([...l]),l=[e])}l.length>0&&t.push(l);for(const s of t){if(s.length<3)continue;const o=s.map((o=>o.content)).join("\n");for(const t of e)if(t.isBlock){const e=d(o,t.content);if(e>=.7){for(const o of s)n.push({type:"块级匹配",lineIndex:o.index,aiRecordId:t.recordId,similarity:e,blockSize:s.length});break}}}return n}function g(o,s){if(!o)return s.split("\n").length;try{const i=t.join(l.tmpdir(),"orig_"+Date.now()),c=t.join(l.tmpdir(),"curr_"+Date.now());n.writeFileSync(i,o),n.writeFileSync(c,s);const r=e(`diff -U0 "${i}" "${c}" | grep '^+[^+]' | wc -l`).toString();return n.unlinkSync(i),n.unlinkSync(c),parseInt(r.trim(),10)}catch(i){return console.error("计算变更行数失败:",i),0}}function f(o){const e=t.extname(o).toLowerCase();return[".js",".jsx",".ts",".tsx",".vue",".html",".css",".scss",".less",".java",".py",".php",".go",".rb",".c",".cpp",".h",".cs",".swift"].includes(e)}function h(o){return o?o.trim().replace(/\s+/g," ").replace(/;\s*$/,"").toLowerCase():""}function d(o,e){if(!o||!e)return 0;if(o===e)return 1;const n=o.length,t=e.length,l=[];for(let c=0;c<=n;c++)l[c]=[c];for(let c=0;c<=t;c++)l[0][c]=c;for(let c=1;c<=n;c++)for(let n=1;n<=t;n++)o[c-1]===e[n-1]?l[c][n]=l[c-1][n-1]:l[c][n]=Math.min(l[c-1][n]+1,l[c][n-1]+1,l[c-1][n-1]+1);const s=l[n][t],i=Math.max(n,t);return 0===i?1:(i-s)/i}function $(o){if(!o)return[];const e=o.split("\n"),n=[];let t=[],l=0;for(let s=0;s<e.length;s++){const o=e[s].trim();!o||o.startsWith("//")||o.startsWith("/*")||o.startsWith("*")?(t.length>=3&&n.push({content:t.join("\n"),startLine:l,endLine:s-1}),t=[],l=s+1):t.push(e[s])}return t.length>=3&&n.push({content:t.join("\n"),startLine:l,endLine:e.length-1}),n}!async function(){const l=Date.now();console.log("🚀 开始AI代码分析...",(new Date).toISOString());try{console.log("📋 解析环境变量...");const c=!1;console.log("🔧 运行模式: "+(c?"开发模式":"生产模式"));const{pr_global_id:h,author:d,source_ref:$,target_ref:u,title:m,GIT_REPO:p,GIT_BRANCH:_}=c?{pr_global_id:"13073502",author:"yaoyan03",source_ref:"feature/init-1.0",target_ref:"master",title:"【AI代码分析工具调试测试】",GIT_REPO:"ssh://*******************/dzfe/medical-home-page.git",GIT_BRANCH:"feature/init-1.0"}:process.env;console.log("📊 PR信息:"),console.log(`  - PR ID: ${h}`),console.log(`  - 标题: ${m}`),console.log(`  - 作者: ${d}`),console.log(`  - 仓库: ${p}`),console.log(`  - 分支: ${_}`),console.log(`  - 源分支: ${$}`),console.log(`  - 目标分支: ${u}`),console.log("🔍 解析Git分支信息...");const y=_||e("git rev-parse --abbrev-ref HEAD").toString().trim(),I=u||"master";console.log(`✅ 当前分支: ${y}, 基准分支: ${I}`),console.log("\n📁 步骤1: 获取Git变更代码...");const A=Date.now(),w=await async function(o,n){const l=[];try{console.log(`🔍 执行Git diff命令: git diff --name-status --diff-filter=AM ${n}..${o}`);const i=e(`git diff --name-status --diff-filter=AM ${n}..${o}`).toString().trim();if(!i)return console.log("⚠️  没有检测到变更文件"),l;const c=i.split("\n");console.log(`📋 检测到 ${c.length} 个文件变更`);for(let r=0;r<c.length;r++){const i=c[r];if(!i)continue;console.log(`\n📄 处理第 ${r+1}/${c.length} 个文件...`);const[a,h]=i.split("\t");if(console.log(`  - 文件: ${h}`),console.log(`  - 状态: ${"A"===a?"新增":"M"===a?"修改":a}`),!f(h)){console.log(`  ⏭️  跳过非代码文件: ${h}`);continue}let d="",$="";if("A"===a){console.log("  📥 获取新增文件内容...");try{const n=`git show ${o}:"${h}"`;console.log(`    执行命令: ${n}`),d=e(n).toString(),$="",console.log(`    ✅ 成功获取文件内容，长度: ${d.length} 字符`)}catch(s){console.error(`    ❌ 获取新增文件 ${h} 内容失败:`,s.message);continue}}else if("M"===a){console.log("  📥 获取修改文件的原始和当前内容...");try{const t=`git show ${n}:"${h}"`;console.log(`    获取原始内容: ${t}`),$=e(t).toString(),console.log(`    ✅ 原始内容长度: ${$.length} 字符`);const l=`git show ${o}:"${h}"`;console.log(`    获取当前内容: ${l}`),d=e(l).toString(),console.log(`    ✅ 当前内容长度: ${d.length} 字符`)}catch(s){console.error(`    ❌ 获取修改文件 ${h} 内容失败:`,s.message);continue}}if(d.trim()){const o=t.extname(h).substring(1);console.log(`  📊 文件类型: ${o}`);const e=d.split("\n").length;console.log(`  📏 总行数: ${e}`),console.log("  🔢 计算变更行数...");const n=g($,d);console.log(`  📈 变更行数: ${n}`);const s={fileName:h,fileType:o,status:a,originalContent:$,currentContent:d,totalLines:e,changedLines:n};l.push(s),console.log(`  ✅ 文件 ${h} 处理完成`)}else console.log(`  ⚠️  文件 ${h} 内容为空，跳过`)}console.log(`\n📊 Git变更文件处理完成，共处理 ${l.length} 个有效文件`)}catch(i){console.error("❌ 获取Git变更失败:",i),console.error("错误详情:",i.stack)}return l}(y,I),L=Date.now()-A;console.log(`✅ 步骤1完成: 获取到 ${w.length} 个变更文件 (耗时: ${L}ms)`),console.log("\n🤖 步骤2: 获取AI生成的代码记录...");const D=Date.now(),S=await async function(e,n){try{const t="https://m.51ping.com/offlinecode/admin/queryCodeGenRecords";console.log(`🌐 准备请求AI代码记录API: ${t}`);const l={createdAt:"",gitUsername:"",gitRepository:e,gitBranch:n,pageNum:1,pageSize:1e3};console.log("📤 请求参数:"),console.log(`  - 仓库: ${e}`),console.log(`  - 分支: ${n}`),console.log(`  - 页码: ${l.pageNum}`),console.log(`  - 页大小: ${l.pageSize}`),console.log("🚀 发送API请求...");const i=Date.now(),c=await o.post(t,l),r=Date.now()-i;if(console.log(`✅ API请求完成 (耗时: ${r}ms)`),console.log(`📊 响应状态: ${c.status}`),console.log("📦 响应数据类型: "+typeof c.data),c.data&&Array.isArray(c.data.list)){const o=c.data.list;console.log(`📋 获取到 ${o.length} 条原始记录`);const e=[];for(let n=0;n<o.length;n++){const t=o[n];console.log(`\n🔄 处理第 ${n+1}/${o.length} 条记录...`),console.log(`  - 记录ID: ${t.id}`),console.log(`  - 用户: ${t.gitUsername}`),console.log(`  - 创建时间: ${t.createdAt}`),console.log(`  - 生成时间戳: ${t.generationTimestamp}`),console.log("  📄 解析生成的代码...");const l=s(t.generatedCode);console.log(`  📁 解析出 ${l.length} 个文件`);const i={id:t.id,timestamp:t.generationTimestamp,gitUsername:t.gitUsername,generatedAt:t.createdAt,files:l};e.push(i),console.log(`  ✅ 记录 ${t.id} 处理完成`)}return console.log(`\n📊 AI记录处理完成，共处理 ${e.length} 条有效记录`),e}return console.log("⚠️  API响应数据格式不正确或为空"),console.log("响应数据:",c.data),[]}catch(t){return console.error("❌ 获取AI生成代码记录失败:",t),t.response&&(console.error("HTTP状态码:",t.response.status),console.error("响应数据:",t.response.data)),console.error("错误详情:",t.stack),[]}}(p,_),x=Date.now()-D;console.log(`✅ 步骤2完成: 获取到 ${S.length} 条AI生成记录 (耗时: ${x}ms)`),console.log("\n🔄 步骤3: 进行代码比对分析...");const N=Date.now(),C=function(o,e){console.log(`🔄 开始代码比对分析，共 ${o.length} 个文件需要分析`);const n=[];for(let t=0;t<o.length;t++){const l=o[t];console.log(`\n📄 分析第 ${t+1}/${o.length} 个文件: ${l.fileName}`),console.log(`  📊 文件信息: ${l.fileType} 类型，${l.totalLines} 总行数，${l.changedLines} 变更行数`),console.log("  🔍 查找相关的AI生成内容...");const s=i(l.fileName,e);console.log(`  📋 找到 ${s.length} 条相关AI生成内容`);const c=l.currentContent.split("\n");console.log(`  📏 文件共 ${c.length} 行`);const g={fileName:l.fileName,fileType:l.fileType,totalLines:l.totalLines,changedLines:l.changedLines,matchedLines:0,aiCodePortion:0,aiMatchDetails:[]};if(s.length>0){console.log("  🔄 开始行级比对...");const o=new Set;let e=0;for(let t=0;t<c.length;t++){const n=c[t].trim();if(!n)continue;const l=r(n,s);l&&(e++,o.add(t),g.aiMatchDetails.push({type:"行级匹配",lineIndex:t,aiRecordId:l.recordId,similarity:l.similarity,lineContent:n}))}console.log(`  ✅ 行级比对完成，匹配 ${e} 行`);const n=[];for(let t=0;t<c.length;t++)o.has(t)||n.push({index:t,content:c[t]});if(n.length>0){console.log(`  🔄 开始块级比对，${n.length} 行未匹配...`);const e=a(n,s);console.log(`  ✅ 块级比对完成，匹配 ${e.length} 行`);for(const n of e)o.add(n.lineIndex),g.aiMatchDetails.push(n)}g.matchedLines=o.size,g.aiCodePortion=l.changedLines>0?o.size/l.changedLines:0,console.log(`  📊 文件 ${l.fileName} 分析结果:`),console.log(`    - 总匹配行数: ${g.matchedLines}`),console.log(`    - AI代码占比: ${(100*g.aiCodePortion).toFixed(2)}%`),console.log(`    - 匹配详情: ${g.aiMatchDetails.length} 条`)}else console.log("  ⚠️  没有找到相关的AI生成内容，跳过比对");n.push(g),console.log(`  ✅ 文件 ${l.fileName} 分析完成`)}return console.log(`\n📊 代码比对分析完成，共分析 ${n.length} 个文件`),n}(w,S),P=Date.now()-N;console.log(`✅ 步骤3完成: 分析了 ${C.length} 个文件 (耗时: ${P}ms)`),console.log("\n📊 步骤4: 生成最终报告...");const T=Date.now(),b=function(o,e,n,t,l){let s=0,i=0,c=0;for(const a of o)s+=a.totalLines,i+=a.changedLines,c+=a.matchedLines;const r=i>0?c/i:0;return{pr_global_id:e,pr_title:n,git_repository:t,git_branch:l,analysis_timestamp:(new Date).toISOString(),summary:{total_files:o.length,total_lines:s,changed_lines:i,ai_generated_lines:c,ai_code_ratio:r},file_details:o.map((o=>({file_name:o.fileName,file_type:o.fileType,total_lines:o.totalLines,changed_lines:o.changedLines,ai_matched_lines:o.matchedLines,ai_code_ratio:o.aiCodePortion})))}}(C,h,m,p,_),k=Date.now()-T;console.log(`✅ 步骤4完成: 报告生成完毕 (耗时: ${k}ms)`);const R=Date.now()-l;console.log(`\n🎉 分析完成! 总耗时: ${R}ms`);const G="ai-analysis-result.json";n.writeFileSync(G,JSON.stringify(b,null,2)),console.log(`💾 结果已保存到: ${G}`),function(o){console.log("\n"+"=".repeat(80)),console.log("🎯 AI代码分析最终报告"),console.log("=".repeat(80)),console.log("\n📋 基本信息:"),console.log(`  PR ID: ${o.pr_global_id}`),console.log(`  标题: ${o.pr_title}`),console.log(`  仓库: ${o.git_repository}`),console.log(`  分支: ${o.git_branch}`),console.log(`  分析时间: ${o.analysis_timestamp}`),console.log("\n📊 总体统计:"),console.log(`  总文件数: ${o.summary.total_files}`),console.log(`  总行数: ${o.summary.total_lines}`),console.log(`  变更行数: ${o.summary.changed_lines}`),console.log(`  AI生成行数: ${o.summary.ai_generated_lines}`),console.log(`  AI代码占比: ${(100*o.summary.ai_code_ratio).toFixed(2)}%`),console.log("\n📁 文件详情:"),o.file_details.forEach(((o,e)=>{console.log(`  ${e+1}. ${o.file_name} (${o.file_type})`),console.log(`     总行数: ${o.total_lines}, 变更行数: ${o.changed_lines}`),console.log(`     AI匹配行数: ${o.ai_matched_lines}, AI占比: ${(100*o.ai_code_ratio).toFixed(2)}%`)})),console.log("\n"+"=".repeat(80)),console.log("✅ 报告输出完成"),console.log("=".repeat(80))}(b)}catch(c){throw console.error("❌ 分析过程出错:",c),console.error("错误堆栈:",c.stack),c}}()}));
