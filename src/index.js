const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function AICodingAnalysis() {
  try {
    // 获取环境变量或使用默认值
    const { pr_global_id, author, source_ref, target_ref, title, GIT_REPO, GIT_BRANCH } = process.env.NODE_ENV === 'development' 
      ? {
          pr_global_id: '13073502',
          author: 'yaoyan03',
          source_ref: 'feature/fedo-213908',
          target_ref: 'master',
          title: '【不要合并，只是测试webhook】',
          GIT_REPO: 'ssh://*******************/dzfe/medical-home-page.git',
          GIT_BRANCH: 'feature/fedo-213908'
        }
      : process.env;
    
    console.log('分析PR:', pr_global_id, title, GIT_REPO, GIT_BRANCH);
    
    // 获取当前分支和基准分支
    const currentBranch = GIT_BRANCH || execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
    const baseBranch = target_ref || 'master';
    console.log(`当前分支: ${currentBranch}, 基准分支: ${baseBranch}`);
    
    // 步骤1: 获取Git变更代码
    const gitChanges = await getGitChanges(currentBranch, baseBranch);
    console.log(`获取到 ${gitChanges.length} 个变更文件`);
    
    // 步骤2: 获取AI生成的代码记录
    const aiGeneratedRecords = await getAIGeneratedRecords(GIT_REPO, GIT_BRANCH);
    console.log(`获取到 ${aiGeneratedRecords.length} 条AI生成记录`);
    
    // 步骤3: 比对分析
    const analysisResults = compareCodeAndCalculateMetrics(gitChanges, aiGeneratedRecords);
    
    // 步骤4: 生成最终结果
    const finalResult = generateFinalReport(analysisResults, pr_global_id, title, GIT_REPO, GIT_BRANCH);
    
    // 输出结果
    console.log('分析完成!');
    fs.writeFileSync('ai-analysis-result.json', JSON.stringify(finalResult, null, 2));
    
    return finalResult;
  } catch (error) {
    console.error('分析过程出错:', error);
    throw error;
  }
}

// 从Git获取变更代码
async function getGitChanges(currentBranch, baseBranch) {
  const result = [];
  
  try {
    // 获取与基准分支比较的变更文件
    const diffCmd = `git diff --name-status --diff-filter=AM ${baseBranch}..${currentBranch}`;
    const diffOutput = execSync(diffCmd).toString().trim();
    
    if (!diffOutput) {
      console.log('没有检测到变更文件');
      return result;
    }
    
    const fileLines = diffOutput.split('\n');
    
    for (const line of fileLines) {
      if (!line) continue;
      
      // 解析状态和文件名
      const [status, fileName] = line.split('\t');
      
      // 过滤非代码文件
      if (!isCodeFile(fileName)) {
        console.log(`跳过非代码文件: ${fileName}`);
        continue;
      }
      
      console.log(`处理文件: ${fileName}, 状态: ${status}`);
      
      let fileContent = '';
      let originalContent = '';
      
      if (status === 'A') {
        // 新增文件 - 获取完整内容
        try {
          fileContent = execSync(`git show ${currentBranch}:"${fileName}"`).toString();
          originalContent = ''; // 新文件没有原始内容
        } catch (err) {
          console.error(`获取新增文件 ${fileName} 内容失败:`, err.message);
          continue;
        }
      } else if (status === 'M') {
        // 修改文件 - 获取原始内容和当前内容
        try {
          // 获取修改前的内容
          originalContent = execSync(`git show ${baseBranch}:"${fileName}"`).toString();
          // 获取当前内容
          fileContent = execSync(`git show ${currentBranch}:"${fileName}"`).toString();
        } catch (err) {
          console.error(`获取修改文件 ${fileName} 内容失败:`, err.message);
          continue;
        }
      }
      
      if (fileContent.trim()) {
        // 提取文件扩展名用于后续处理
        const fileType = path.extname(fileName).substring(1);
        
        // 计算文件的总行数和修改行数
        const totalLines = fileContent.split('\n').length;
        const changedLines = calculateChangedLines(originalContent, fileContent);
        
        result.push({
          fileName,
          fileType,
          status,
          originalContent,
          currentContent: fileContent,
          totalLines,
          changedLines
        });
      }
    }
  } catch (error) {
    console.error('获取Git变更失败:', error);
  }
  
  return result;
}

// 获取AI生成的代码记录
async function getAIGeneratedRecords(gitRepo, gitBranch) {
  try {
    const url = 'https://m.51ping.com/offlinecode/admin/queryCodeGenRecords';    
 
    const data = {
      createdAt: '',
      gitUsername: '',
      gitRepository: gitRepo,
      gitBranch: gitBranch,
      pageNum: 1,
      pageSize: 1000
    };
    
    const response = await axios.post(url, data);
    
    if (response.data && Array.isArray(response.data.list)) {
      // 处理AI生成的代码记录
      return response.data.list.map(record => {
        // 解析generatedCode字段，提取文件和代码内容
        const files = parseGeneratedCode(record.generatedCode);
        return {
          id: record.id,
          timestamp: record.generationTimestamp,
          gitUsername: record.gitUsername,
          generatedAt: record.createdAt,
          files
        };
      });
    }
    
    return [];
  } catch (error) {
    console.error('获取AI生成代码记录失败:', error);
    return [];
  }
}

// 解析AI生成的代码
function parseGeneratedCode(generatedCode) {
  if (!generatedCode) return [];
  
  const files = [];
  const fileRegex = /--- FILE: ([^\s]+) ---\n([\s\S]*?)(?=--- FILE:|$)/g;
  
  let match;
  while ((match = fileRegex.exec(generatedCode)) !== null) {
    const fileName = match[1];
    const content = match[2].trim();
    
    files.push({
      fileName,
      content
    });
  }
  
  return files;
}

// 比对代码并计算指标 - 优化版
function compareCodeAndCalculateMetrics(gitChanges, aiGeneratedRecords) {
  const results = [];
  
  for (const file of gitChanges) {
    // 获取这个文件的所有AI生成记录
    const allAIGeneratedContent = getAllAIGeneratedContentForFile(file.fileName, aiGeneratedRecords);
    
    // 获取文件内容进行比对
    const fileContent = file.currentContent;
    const fileLines = fileContent.split('\n');
    
    // 存储匹配结果
    const matchResults = {
      fileName: file.fileName,
      fileType: file.fileType,
      totalLines: file.totalLines,
      changedLines: file.changedLines,
      matchedLines: 0,
      aiCodePortion: 0,
      aiMatchDetails: []
    };
    
    // 如果有AI生成记录，进行比对
    if (allAIGeneratedContent.length > 0) {
      const matchedLineSet = new Set(); // 用于跟踪已匹配的行号
      
      // 对每一行进行比对
      for (let i = 0; i < fileLines.length; i++) {
        const line = fileLines[i].trim();
        if (!line) continue; // 跳过空行
        
        // 检查这一行是否在任何AI生成记录中出现
        const match = findBestLineMatch(line, allAIGeneratedContent);
        if (match) {
          matchedLineSet.add(i);
          matchResults.aiMatchDetails.push({
            type: '行级匹配',
            lineIndex: i,
            aiRecordId: match.recordId,
            similarity: match.similarity,
            lineContent: line
          });
        }
      }
      
      // 进行块级比对(对于没有匹配到的行)
      const unmatchedLines = [];
      for (let i = 0; i < fileLines.length; i++) {
        if (!matchedLineSet.has(i)) {
          unmatchedLines.push({ index: i, content: fileLines[i] });
        }
      }
      
      if (unmatchedLines.length > 0) {
        const blockMatches = performBlockBasedComparisonForUnmatched(unmatchedLines, allAIGeneratedContent);
        
        // 将块级匹配的行添加到已匹配集合
        for (const match of blockMatches) {
          matchedLineSet.add(match.lineIndex);
          matchResults.aiMatchDetails.push(match);
        }
      }
      
      // 更新匹配结果
      matchResults.matchedLines = matchedLineSet.size;
      matchResults.aiCodePortion = file.changedLines > 0 ? 
        (matchedLineSet.size / file.changedLines) : 0;
    }
    
    results.push(matchResults);
  }
  
  return results;
}

// 获取文件的所有AI生成内容
function getAllAIGeneratedContentForFile(fileName, aiGeneratedRecords) {
  const result = [];
  
  for (const record of aiGeneratedRecords) {
    for (const file of record.files) {
      // 检查文件名是否匹配或相关
      if (isFileNameRelated(fileName, file.fileName)) {
        // 按行分割AI生成的代码
        const lines = file.content.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line && !line.startsWith('//') && !line.startsWith('/*') && !line.startsWith('*')) {
            result.push({
              line,
              recordId: record.id,
              lineIndex: i,
              fileName: file.fileName
            });
          }
        }
        
        // 也添加代码块，用于后续的块级比较
        const blocks = splitIntoCodeBlocks(file.content);
        for (const block of blocks) {
          result.push({
            isBlock: true,
            content: block.content,
            recordId: record.id,
            startLine: block.startLine,
            endLine: block.endLine,
            fileName: file.fileName
          });
        }
      }
    }
  }
  
  return result;
}

// 判断两个文件名是否相关
function isFileNameRelated(fileName1, fileName2) {
  // 完全匹配
  if (fileName1 === fileName2) return true;
  
  // 路径中包含关系
  if (fileName1.endsWith('/' + fileName2) || fileName2.endsWith('/' + fileName1)) return true;
  
  // 提取基本文件名（不含路径）进行比较
  const baseName1 = fileName1.split('/').pop();
  const baseName2 = fileName2.split('/').pop();
  
  return baseName1 === baseName2;
}

// 寻找最佳行匹配
function findBestLineMatch(line, allAIGeneratedContent) {
  const normalizedLine = normalizeCode(line);
  if (!normalizedLine) return null;
  
  // 先尝试精确匹配
  for (const content of allAIGeneratedContent) {
    if (!content.isBlock && normalizeCode(content.line) === normalizedLine) {
      return {
        recordId: content.recordId,
        similarity: 1.0
      };
    }
  }
  
  // 如果没有精确匹配，尝试相似度匹配
  let bestMatch = null;
  let highestSimilarity = 0.8; // 最低相似度阈值
  
  for (const content of allAIGeneratedContent) {
    if (!content.isBlock) {
      const similarity = calculateSimilarity(normalizedLine, normalizeCode(content.line));
      if (similarity > highestSimilarity) {
        highestSimilarity = similarity;
        bestMatch = {
          recordId: content.recordId,
          similarity
        };
      }
    }
  }
  
  return bestMatch;
}

// 对未匹配行进行块级比对
function performBlockBasedComparisonForUnmatched(unmatchedLines, allAIGeneratedContent) {
  const blockMatches = [];
  
  // 将未匹配行分组为连续块
  const lineBlocks = [];
  let currentBlock = [];
  
  for (let i = 0; i < unmatchedLines.length; i++) {
    const current = unmatchedLines[i];
    
    if (i === 0 || current.index === unmatchedLines[i-1].index + 1) {
      // 连续行，添加到当前块
      currentBlock.push(current);
    } else {
      // 不连续，开始新块
      if (currentBlock.length > 0) {
        lineBlocks.push([...currentBlock]);
      }
      currentBlock = [current];
    }
  }
  
  // 添加最后一个块
  if (currentBlock.length > 0) {
    lineBlocks.push(currentBlock);
  }
  
  // 对每个未匹配块进行比对
  for (const block of lineBlocks) {
    if (block.length < 3) continue; // 忽略太小的块
    
    const blockContent = block.map(item => item.content).join('\n');
    
    // 在AI生成内容中寻找匹配块
    for (const aiContent of allAIGeneratedContent) {
      if (aiContent.isBlock) {
        const similarity = calculateSimilarity(blockContent, aiContent.content);
        
        if (similarity >= 0.7) { // 块级匹配使用较低的阈值
          // 将匹配到的块的所有行都标记为匹配
          for (const lineItem of block) {
            blockMatches.push({
              type: '块级匹配',
              lineIndex: lineItem.index,
              aiRecordId: aiContent.recordId,
              similarity: similarity,
              blockSize: block.length
            });
          }
          break; // 找到匹配后退出内循环
        }
      }
    }
  }
  
  return blockMatches;
}

// 计算修改的行数
function calculateChangedLines(originalContent, currentContent) {
  if (!originalContent) return currentContent.split('\n').length; // 新文件所有行都是变更
  
  // 使用diff工具计算修改的行数
  try {
    const tempOrigFile = path.join(os.tmpdir(), 'orig_' + Date.now());
    const tempCurrFile = path.join(os.tmpdir(), 'curr_' + Date.now());
    
    fs.writeFileSync(tempOrigFile, originalContent);
    fs.writeFileSync(tempCurrFile, currentContent);
    
    const diffOutput = execSync(`diff -U0 "${tempOrigFile}" "${tempCurrFile}" | grep '^+[^+]' | wc -l`).toString();
    
    fs.unlinkSync(tempOrigFile);
    fs.unlinkSync(tempCurrFile);
    
    return parseInt(diffOutput.trim(), 10);
  } catch (error) {
    console.error('计算变更行数失败:', error);
    return 0;
  }
}

// 检查是否为代码文件
function isCodeFile(fileName) {
  const codeExtensions = [
    '.js', '.jsx', '.ts', '.tsx', '.vue', '.html', '.css', '.scss', '.less',
    '.java', '.py', '.php', '.go', '.rb', '.c', '.cpp', '.h', '.cs', '.swift'
  ];
  
  const ext = path.extname(fileName).toLowerCase();
  return codeExtensions.includes(ext);
}

// 生成最终报告
function generateFinalReport(analysisResults, prId, title, gitRepo, gitBranch) {
  // 计算总体统计数据
  let totalLines = 0;
  let totalChangedLines = 0;
  let totalAILines = 0;
  
  for (const result of analysisResults) {
    totalLines += result.totalLines;
    totalChangedLines += result.changedLines;
    totalAILines += result.matchedLines;
  }
  
  const overallAIRatio = totalChangedLines > 0 ? (totalAILines / totalChangedLines) : 0;
  
  // 构建最终结果
  return {
    pr_global_id: prId,
    pr_title: title,
    git_repository: gitRepo,
    git_branch: gitBranch,
    analysis_timestamp: new Date().toISOString(),
    summary: {
      total_files: analysisResults.length,
      total_lines: totalLines,
      changed_lines: totalChangedLines,
      ai_generated_lines: totalAILines,
      ai_code_ratio: overallAIRatio
    },
    file_details: analysisResults.map(result => ({
      file_name: result.fileName,
      file_type: result.fileType,
      total_lines: result.totalLines,
      changed_lines: result.changedLines,
      ai_matched_lines: result.matchedLines,
      ai_code_ratio: result.aiCodePortion
    }))
  };
}

// 导出函数
module.exports = AICodingAnalysis;

// 如果直接运行脚本
if (require.main === module) {
  AICodingAnalysis().catch(console.error);
}