const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

async function AICodingAnalysis() {
  const startTime = Date.now();
  console.log('🚀 开始AI代码分析...', new Date().toISOString());

  try {
    // 获取环境变量或使用默认值
    console.log('📋 解析环境变量...');
    const isDevelopment = process.env.NODE_ENV === 'development';
    console.log(`🔧 运行模式: ${isDevelopment ? '开发模式' : '生产模式'}`);

    const { pr_global_id, author, source_ref, target_ref, title, GIT_REPO, GIT_BRANCH } = isDevelopment
      ? {
          pr_global_id: '13073502',
          author: 'yaoyan03',
          source_ref: 'feature/fedo-213908',
          target_ref: 'master',
          title: '【不要合并，只是测试webhook】',
          GIT_REPO: 'ssh://*******************/dzfe/medical-home-page.git',
          GIT_BRANCH: 'feature/fedo-213908'
        }
      : process.env;

    console.log('📊 PR信息:');
    console.log(`  - PR ID: ${pr_global_id}`);
    console.log(`  - 标题: ${title}`);
    console.log(`  - 作者: ${author}`);
    console.log(`  - 仓库: ${GIT_REPO}`);
    console.log(`  - 分支: ${GIT_BRANCH}`);
    console.log(`  - 源分支: ${source_ref}`);
    console.log(`  - 目标分支: ${target_ref}`);

    // 获取当前分支和基准分支
    console.log('🔍 解析Git分支信息...');
    const currentBranch = GIT_BRANCH || execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
    const baseBranch = target_ref || 'master';
    console.log(`✅ 当前分支: ${currentBranch}, 基准分支: ${baseBranch}`);
    console.log(`📍 目标仓库: ${GIT_REPO}`);

    // 步骤1: 获取Git变更代码
    console.log('\n📁 步骤1: 获取Git变更代码...');
    const step1StartTime = Date.now();
    const gitChanges = await getGitChanges(currentBranch, baseBranch, GIT_REPO);
    const step1Duration = Date.now() - step1StartTime;
    console.log(`✅ 步骤1完成: 获取到 ${gitChanges.length} 个变更文件 (耗时: ${step1Duration}ms)`);

    // 步骤2: 获取AI生成的代码记录
    console.log('\n🤖 步骤2: 获取AI生成的代码记录...');
    const step2StartTime = Date.now();
    const aiGeneratedRecords = await getAIGeneratedRecords(GIT_REPO, GIT_BRANCH);
    const step2Duration = Date.now() - step2StartTime;
    console.log(`✅ 步骤2完成: 获取到 ${aiGeneratedRecords.length} 条AI生成记录 (耗时: ${step2Duration}ms)`);

    // 步骤3: 比对分析
    console.log('\n🔄 步骤3: 进行代码比对分析...');
    const step3StartTime = Date.now();
    const analysisResults = compareCodeAndCalculateMetrics(gitChanges, aiGeneratedRecords);
    const step3Duration = Date.now() - step3StartTime;
    console.log(`✅ 步骤3完成: 分析了 ${analysisResults.length} 个文件 (耗时: ${step3Duration}ms)`);

    // 步骤4: 生成最终结果
    console.log('\n📊 步骤4: 生成最终报告...');
    const step4StartTime = Date.now();
    const finalResult = generateFinalReport(analysisResults, pr_global_id, title, GIT_REPO, GIT_BRANCH);
    const step4Duration = Date.now() - step4StartTime;
    console.log(`✅ 步骤4完成: 报告生成完毕 (耗时: ${step4Duration}ms)`);

    // 输出结果
    const totalDuration = Date.now() - startTime;
    console.log(`\n🎉 分析完成! 总耗时: ${totalDuration}ms`);

    // 使用console打印最终报告
    printFinalReport(finalResult);

    return finalResult;
  } catch (error) {
    console.error('❌ 分析过程出错:', error);
    console.error('错误堆栈:', error.stack);
    throw error;
  }
}

// 从Git获取变更代码
async function getGitChanges(currentBranch, baseBranch, gitRepo) {
  const result = [];

  try {
    // 检查是否需要操作远程仓库
    const isRemoteRepo = gitRepo && !gitRepo.includes(process.cwd()) && gitRepo.startsWith('ssh://');
    let workingDir = process.cwd();
    let tempDir = null;

    if (isRemoteRepo) {
      console.log(`🌐 检测到远程仓库，准备克隆: ${gitRepo}`);

      // 创建临时目录
      const tempDirName = `temp-repo-${Date.now()}`;
      tempDir = path.join(os.tmpdir(), tempDirName);

      try {
        console.log(`📁 创建临时目录: ${tempDir}`);
        fs.mkdirSync(tempDir, { recursive: true });

        console.log(`🔄 克隆仓库到临时目录...`);
        execSync(`git clone ${gitRepo} ${tempDir}`, { stdio: 'pipe' });
        console.log(`✅ 仓库克隆完成`);

        workingDir = tempDir;

        // 切换到临时目录并fetch所有分支
        console.log(`🔄 获取所有远程分支...`);
        execSync('git fetch --all', { cwd: workingDir, stdio: 'pipe' });
        console.log(`✅ 远程分支获取完成`);

      } catch (cloneErr) {
        console.error(`❌ 克隆仓库失败:`, cloneErr.message);

        // 清理临时目录
        if (tempDir && fs.existsSync(tempDir)) {
          try {
            fs.rmSync(tempDir, { recursive: true, force: true });
          } catch (cleanupErr) {
            console.error(`⚠️  清理临时目录失败:`, cleanupErr.message);
          }
        }

        throw cloneErr;
      }
    }

    // 在工作目录中检查分支是否存在
    console.log(`🔍 在目录 ${workingDir} 中检查分支...`);

    // 检查当前分支
    let currentBranchExists = false;
    try {
      execSync(`git rev-parse --verify ${currentBranch}`, { cwd: workingDir, stdio: 'pipe' });
      console.log(`✅ 当前分支 ${currentBranch} 存在`);
      currentBranchExists = true;
    } catch (err) {
      console.log(`⚠️  本地分支 ${currentBranch} 不存在，尝试其他方式...`);

      // 尝试检查远程分支
      try {
        execSync(`git rev-parse --verify origin/${currentBranch}`, { cwd: workingDir, stdio: 'pipe' });
        console.log(`✅ 远程分支 origin/${currentBranch} 存在`);
        currentBranch = `origin/${currentBranch}`;
        currentBranchExists = true;
      } catch (err2) {
        console.log(`⚠️  远程分支 origin/${currentBranch} 也不存在`);

        // 如果是远程仓库，列出所有可用分支
        if (isRemoteRepo) {
          try {
            const branches = execSync('git branch -a', { cwd: workingDir }).toString();
            console.log(`📋 可用分支列表:\n${branches}`);
          } catch (listErr) {
            console.log(`⚠️  无法列出分支:`, listErr.message);
          }
        }

        throw new Error(`分支 ${currentBranch} 在仓库中不存在`);
      }
    }

    try {
      execSync(`git rev-parse --verify ${baseBranch}`, { cwd: workingDir, stdio: 'pipe' });
      console.log(`✅ 基准分支 ${baseBranch} 存在`);
    } catch (err) {
      console.log(`⚠️  基准分支 ${baseBranch} 不存在，尝试远程分支...`);
      try {
        execSync(`git rev-parse --verify origin/${baseBranch}`, { cwd: workingDir, stdio: 'pipe' });
        baseBranch = `origin/${baseBranch}`;
        console.log(`✅ 使用远程分支: ${baseBranch}`);
      } catch (err2) {
        console.log(`⚠️  远程分支 origin/${baseBranch} 也不存在，使用HEAD~1`);
        baseBranch = 'HEAD~1';
      }
    }

    // 获取与基准分支比较的变更文件
    console.log(`🔍 执行Git diff命令: git diff --name-status --diff-filter=AM ${baseBranch}..${currentBranch}`);
    const diffCmd = `git diff --name-status --diff-filter=AM ${baseBranch}..${currentBranch}`;
    const diffOutput = execSync(diffCmd, { cwd: workingDir }).toString().trim();

    if (!diffOutput) {
      console.log('⚠️  没有检测到变更文件，尝试其他策略...');

      // 策略1: 尝试获取最近的提交变更
      console.log('🔄 策略1: 获取最近提交的变更...');
      try {
        const recentDiffCmd = `git diff --name-status HEAD~1 HEAD`;
        console.log(`执行命令: ${recentDiffCmd}`);
        const recentDiffOutput = execSync(recentDiffCmd, { cwd: workingDir }).toString().trim();
        if (recentDiffOutput) {
          console.log(`✅ 找到最近提交的变更: ${recentDiffOutput.split('\n').length} 个文件`);
          return processGitDiffOutput(recentDiffOutput, currentBranch, 'HEAD~1', workingDir);
        }
      } catch (err) {
        console.log('❌ 策略1失败:', err.message);
      }

      // 策略2: 获取工作目录中的未提交变更
      console.log('🔄 策略2: 获取工作目录中的变更...');
      try {
        const workingDiffCmd = `git diff --name-status`;
        console.log(`执行命令: ${workingDiffCmd}`);
        const workingDiffOutput = execSync(workingDiffCmd, { cwd: workingDir }).toString().trim();
        if (workingDiffOutput) {
          console.log(`✅ 找到工作目录变更: ${workingDiffOutput.split('\n').length} 个文件`);
          return processGitDiffOutput(workingDiffOutput, 'working-directory', 'HEAD', workingDir);
        }
      } catch (err) {
        console.log('❌ 策略2失败:', err.message);
      }

      // 策略3: 获取暂存区变更
      console.log('🔄 策略3: 获取暂存区变更...');
      try {
        const stagedDiffCmd = `git diff --name-status --cached`;
        console.log(`执行命令: ${stagedDiffCmd}`);
        const stagedDiffOutput = execSync(stagedDiffCmd, { cwd: workingDir }).toString().trim();
        if (stagedDiffOutput) {
          console.log(`✅ 找到暂存区变更: ${stagedDiffOutput.split('\n').length} 个文件`);
          return processGitDiffOutput(stagedDiffOutput, 'staged', 'HEAD', workingDir);
        }
      } catch (err) {
        console.log('❌ 策略3失败:', err.message);
      }

      console.log('⚠️  所有策略都未找到变更文件');
      return result;
    }

    const gitResult = processGitDiffOutput(diffOutput, currentBranch, baseBranch, workingDir);

    // 清理临时目录
    if (tempDir && fs.existsSync(tempDir)) {
      try {
        console.log(`🧹 清理临时目录: ${tempDir}`);
        fs.rmSync(tempDir, { recursive: true, force: true });
        console.log(`✅ 临时目录清理完成`);
      } catch (cleanupErr) {
        console.error(`⚠️  清理临时目录失败:`, cleanupErr.message);
      }
    }

    return gitResult;
  } catch (error) {
    console.error('❌ 获取Git变更失败:', error);
    console.error('错误详情:', error.stack);
  }

  return result;
}

// 处理Git diff输出的通用函数
function processGitDiffOutput(diffOutput, currentBranch, baseBranch, workingDir = process.cwd()) {
  const result = [];
  const fileLines = diffOutput.split('\n');
  console.log(`📋 检测到 ${fileLines.length} 个文件变更`);

  for (let i = 0; i < fileLines.length; i++) {
    const line = fileLines[i];
    if (!line) continue;

    console.log(`\n📄 处理第 ${i + 1}/${fileLines.length} 个文件...`);

    // 解析状态和文件名
    const [status, fileName] = line.split('\t');
    console.log(`  - 文件: ${fileName}`);
    console.log(`  - 状态: ${status === 'A' ? '新增' : status === 'M' ? '修改' : status}`);

    // 过滤非代码文件
    if (!isCodeFile(fileName)) {
      console.log(`  ⏭️  跳过非代码文件: ${fileName}`);
      continue;
    }

    let fileContent = '';
    let originalContent = '';

    if (status === 'A') {
      // 新增文件 - 获取完整内容
      console.log(`  📥 获取新增文件内容...`);
      try {
        let getContentCmd;
        if (currentBranch === 'working-directory') {
          // 工作目录文件，直接读取
          getContentCmd = `cat "${fileName}"`;
          console.log(`    执行命令: ${getContentCmd}`);
          fileContent = execSync(getContentCmd, { cwd: workingDir }).toString();
        } else if (currentBranch === 'staged') {
          // 暂存区文件
          getContentCmd = `git show :${fileName}`;
          console.log(`    执行命令: ${getContentCmd}`);
          fileContent = execSync(getContentCmd, { cwd: workingDir }).toString();
        } else {
          // Git分支文件
          getContentCmd = `git show "${currentBranch}:${fileName}"`;
          console.log(`    执行命令: ${getContentCmd}`);
          try {
            fileContent = execSync(getContentCmd, { cwd: workingDir }).toString();
          } catch (gitErr) {
            console.error(`    ❌ Git show命令失败: ${gitErr.message}`);
            console.log(`    🔄 尝试备用命令格式...`);
            // 尝试不同的命令格式
            const altCmd = `git show '${currentBranch}:${fileName}'`;
            console.log(`    备用命令: ${altCmd}`);
            fileContent = execSync(altCmd, { cwd: workingDir }).toString();
          }
        }
        originalContent = ''; // 新文件没有原始内容
        console.log(`    ✅ 成功获取文件内容，长度: ${fileContent.length} 字符`);
      } catch (err) {
        console.error(`    ❌ 获取新增文件 ${fileName} 内容失败:`, err.message);
        continue;
      }
    } else if (status === 'M') {
      // 修改文件 - 获取原始内容和当前内容
      console.log(`  📥 获取修改文件的原始和当前内容...`);
      try {
        // 获取修改前的内容
        let getOriginalCmd;
        if (baseBranch === 'HEAD') {
          getOriginalCmd = `git show "HEAD:${fileName}"`;
        } else {
          getOriginalCmd = `git show "${baseBranch}:${fileName}"`;
        }
        console.log(`    获取原始内容: ${getOriginalCmd}`);
        try {
          originalContent = execSync(getOriginalCmd, { cwd: workingDir }).toString();
        } catch (gitErr) {
          console.error(`    ❌ 获取原始内容失败: ${gitErr.message}`);
          console.log(`    🔄 尝试备用命令格式...`);
          const altOriginalCmd = baseBranch === 'HEAD' ?
            `git show 'HEAD:${fileName}'` :
            `git show '${baseBranch}:${fileName}'`;
          console.log(`    备用命令: ${altOriginalCmd}`);
          originalContent = execSync(altOriginalCmd, { cwd: workingDir }).toString();
        }
        console.log(`    ✅ 原始内容长度: ${originalContent.length} 字符`);

        // 获取当前内容
        let getCurrentCmd;
        if (currentBranch === 'working-directory') {
          getCurrentCmd = `cat "${fileName}"`;
        } else if (currentBranch === 'staged') {
          getCurrentCmd = `git show :${fileName}`;
        } else {
          getCurrentCmd = `git show "${currentBranch}:${fileName}"`;
        }
        console.log(`    获取当前内容: ${getCurrentCmd}`);
        try {
          fileContent = execSync(getCurrentCmd, { cwd: workingDir }).toString();
        } catch (gitErr) {
          console.error(`    ❌ 获取当前内容失败: ${gitErr.message}`);
          console.log(`    🔄 尝试备用命令格式...`);
          const altCurrentCmd = currentBranch === 'working-directory' ?
            `cat "${fileName}"` :
            currentBranch === 'staged' ?
            `git show :${fileName}` :
            `git show '${currentBranch}:${fileName}'`;
          console.log(`    备用命令: ${altCurrentCmd}`);
          fileContent = execSync(altCurrentCmd, { cwd: workingDir }).toString();
        }
        console.log(`    ✅ 当前内容长度: ${fileContent.length} 字符`);
      } catch (err) {
        console.error(`    ❌ 获取修改文件 ${fileName} 内容失败:`, err.message);
        continue;
      }
    }

    if (fileContent.trim()) {
      // 提取文件扩展名用于后续处理
      const fileType = path.extname(fileName).substring(1);
      console.log(`  📊 文件类型: ${fileType}`);

      // 计算文件的总行数和修改行数
      const totalLines = fileContent.split('\n').length;
      console.log(`  📏 总行数: ${totalLines}`);

      console.log(`  🔢 计算变更行数...`);
      const changedLines = calculateChangedLines(originalContent, fileContent);
      console.log(`  📈 变更行数: ${changedLines}`);

      const fileInfo = {
        fileName,
        fileType,
        status,
        originalContent,
        currentContent: fileContent,
        totalLines,
        changedLines
      };

      result.push(fileInfo);
      console.log(`  ✅ 文件 ${fileName} 处理完成`);
    } else {
      console.log(`  ⚠️  文件 ${fileName} 内容为空，跳过`);
    }
  }

  console.log(`\n📊 Git变更文件处理完成，共处理 ${result.length} 个有效文件`);
  return result;
}

// 获取AI生成的代码记录
async function getAIGeneratedRecords(gitRepo, gitBranch) {
  try {
    const url = 'https://m.51ping.com/offlinecode/admin/queryCodeGenRecords';
    console.log(`🌐 准备请求AI代码记录API: ${url}`);

    const data = {
      createdAt: '',
      gitUsername: '',
      gitRepository: gitRepo,
      gitBranch: gitBranch,
      pageNum: 1,
      pageSize: 1000
    };

    console.log('📤 请求参数:');
    console.log(`  - 仓库: ${gitRepo}`);
    console.log(`  - 分支: ${gitBranch}`);
    console.log(`  - 页码: ${data.pageNum}`);
    console.log(`  - 页大小: ${data.pageSize}`);

    console.log('🚀 发送API请求...');
    const requestStartTime = Date.now();
    const response = await axios.post(url, data);
    const requestDuration = Date.now() - requestStartTime;

    console.log(`✅ API请求完成 (耗时: ${requestDuration}ms)`);
    console.log(`📊 响应状态: ${response.status}`);
    console.log(`📦 响应数据类型: ${typeof response.data}`);

    if (response.data && Array.isArray(response.data.list)) {
      const rawRecords = response.data.list;
      console.log(`📋 获取到 ${rawRecords.length} 条原始记录`);

      // 处理AI生成的代码记录
      const processedRecords = [];
      for (let i = 0; i < rawRecords.length; i++) {
        const record = rawRecords[i];
        console.log(`\n🔄 处理第 ${i + 1}/${rawRecords.length} 条记录...`);
        console.log(`  - 记录ID: ${record.id}`);
        console.log(`  - 用户: ${record.gitUsername}`);
        console.log(`  - 创建时间: ${record.createdAt}`);
        console.log(`  - 生成时间戳: ${record.generationTimestamp}`);

        // 解析generatedCode字段，提取文件和代码内容
        console.log(`  📄 解析生成的代码...`);
        const files = parseGeneratedCode(record.generatedCode);
        console.log(`  📁 解析出 ${files.length} 个文件`);

        const processedRecord = {
          id: record.id,
          timestamp: record.generationTimestamp,
          gitUsername: record.gitUsername,
          generatedAt: record.createdAt,
          files
        };

        processedRecords.push(processedRecord);
        console.log(`  ✅ 记录 ${record.id} 处理完成`);
      }

      console.log(`\n📊 AI记录处理完成，共处理 ${processedRecords.length} 条有效记录`);
      return processedRecords;
    } else {
      console.log('⚠️  API响应数据格式不正确或为空');
      console.log('响应数据:', response.data);
      return [];
    }
  } catch (error) {
    console.error('❌ 获取AI生成代码记录失败:', error);
    if (error.response) {
      console.error('HTTP状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    console.error('错误详情:', error.stack);
    return [];
  }
}

// 解析AI生成的代码
function parseGeneratedCode(generatedCode) {
  if (!generatedCode) {
    console.log('    ⚠️  生成代码为空');
    return [];
  }

  console.log(`    📝 开始解析生成代码，长度: ${generatedCode.length} 字符`);

  const files = [];
  const fileRegex = /--- FILE: ([^\s]+) ---\n([\s\S]*?)(?=--- FILE:|$)/g;

  let match;
  let matchCount = 0;
  while ((match = fileRegex.exec(generatedCode)) !== null) {
    matchCount++;
    const fileName = match[1];
    const content = match[2].trim();

    console.log(`      📄 解析文件 ${matchCount}: ${fileName}`);
    console.log(`      📏 文件内容长度: ${content.length} 字符`);
    console.log(`      📊 文件行数: ${content.split('\n').length} 行`);

    files.push({
      fileName,
      content
    });
  }

  console.log(`    ✅ 解析完成，共找到 ${files.length} 个文件`);
  return files;
}

// 比对代码并计算指标 - 优化版
function compareCodeAndCalculateMetrics(gitChanges, aiGeneratedRecords) {
  console.log(`🔄 开始代码比对分析，共 ${gitChanges.length} 个文件需要分析`);
  const results = [];

  for (let fileIndex = 0; fileIndex < gitChanges.length; fileIndex++) {
    const file = gitChanges[fileIndex];
    console.log(`\n📄 分析第 ${fileIndex + 1}/${gitChanges.length} 个文件: ${file.fileName}`);
    console.log(`  📊 文件信息: ${file.fileType} 类型，${file.totalLines} 总行数，${file.changedLines} 变更行数`);

    // 获取这个文件的所有AI生成记录
    console.log(`  🔍 查找相关的AI生成内容...`);
    const allAIGeneratedContent = getAllAIGeneratedContentForFile(file.fileName, aiGeneratedRecords);
    console.log(`  📋 找到 ${allAIGeneratedContent.length} 条相关AI生成内容`);

    // 获取文件内容进行比对
    const fileContent = file.currentContent;
    const fileLines = fileContent.split('\n');
    console.log(`  📏 文件共 ${fileLines.length} 行`);

    // 存储匹配结果
    const matchResults = {
      fileName: file.fileName,
      fileType: file.fileType,
      totalLines: file.totalLines,
      changedLines: file.changedLines,
      matchedLines: 0,
      aiCodePortion: 0,
      aiMatchDetails: []
    };

    // 如果有AI生成记录，进行比对
    if (allAIGeneratedContent.length > 0) {
      console.log(`  🔄 开始行级比对...`);
      const matchedLineSet = new Set(); // 用于跟踪已匹配的行号

      // 对每一行进行比对
      let lineMatchCount = 0;
      for (let i = 0; i < fileLines.length; i++) {
        const line = fileLines[i].trim();
        if (!line) continue; // 跳过空行

        // 检查这一行是否在任何AI生成记录中出现
        const match = findBestLineMatch(line, allAIGeneratedContent);
        if (match) {
          lineMatchCount++;
          matchedLineSet.add(i);
          matchResults.aiMatchDetails.push({
            type: '行级匹配',
            lineIndex: i,
            aiRecordId: match.recordId,
            similarity: match.similarity,
            lineContent: line
          });
        }
      }
      console.log(`  ✅ 行级比对完成，匹配 ${lineMatchCount} 行`);

      // 进行块级比对(对于没有匹配到的行)
      const unmatchedLines = [];
      for (let i = 0; i < fileLines.length; i++) {
        if (!matchedLineSet.has(i)) {
          unmatchedLines.push({ index: i, content: fileLines[i] });
        }
      }

      if (unmatchedLines.length > 0) {
        console.log(`  🔄 开始块级比对，${unmatchedLines.length} 行未匹配...`);
        const blockMatches = performBlockBasedComparisonForUnmatched(unmatchedLines, allAIGeneratedContent);
        console.log(`  ✅ 块级比对完成，匹配 ${blockMatches.length} 行`);

        // 将块级匹配的行添加到已匹配集合
        for (const match of blockMatches) {
          matchedLineSet.add(match.lineIndex);
          matchResults.aiMatchDetails.push(match);
        }
      }

      // 更新匹配结果
      matchResults.matchedLines = matchedLineSet.size;
      matchResults.aiCodePortion = file.changedLines > 0 ?
        (matchedLineSet.size / file.changedLines) : 0;

      console.log(`  📊 文件 ${file.fileName} 分析结果:`);
      console.log(`    - 总匹配行数: ${matchResults.matchedLines}`);
      console.log(`    - AI代码占比: ${(matchResults.aiCodePortion * 100).toFixed(2)}%`);
      console.log(`    - 匹配详情: ${matchResults.aiMatchDetails.length} 条`);
    } else {
      console.log(`  ⚠️  没有找到相关的AI生成内容，跳过比对`);
    }

    results.push(matchResults);
    console.log(`  ✅ 文件 ${file.fileName} 分析完成`);
  }

  console.log(`\n📊 代码比对分析完成，共分析 ${results.length} 个文件`);
  return results;
}

// 获取文件的所有AI生成内容
function getAllAIGeneratedContentForFile(fileName, aiGeneratedRecords) {
  const result = [];

  for (const record of aiGeneratedRecords) {
    for (const file of record.files) {
      // 检查文件名是否匹配或相关
      if (isFileNameRelated(fileName, file.fileName)) {
        // 按行分割AI生成的代码
        const lines = file.content.split('\n');
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line && !line.startsWith('//') && !line.startsWith('/*') && !line.startsWith('*')) {
            result.push({
              line,
              recordId: record.id,
              lineIndex: i,
              fileName: file.fileName
            });
          }
        }

        // 也添加代码块，用于后续的块级比较
        const blocks = splitIntoCodeBlocks(file.content);
        for (const block of blocks) {
          result.push({
            isBlock: true,
            content: block.content,
            recordId: record.id,
            startLine: block.startLine,
            endLine: block.endLine,
            fileName: file.fileName
          });
        }
      }
    }
  }

  return result;
}

// 判断两个文件名是否相关
function isFileNameRelated(fileName1, fileName2) {
  // 完全匹配
  if (fileName1 === fileName2) return true;

  // 路径中包含关系
  if (fileName1.endsWith('/' + fileName2) || fileName2.endsWith('/' + fileName1)) return true;

  // 提取基本文件名（不含路径）进行比较
  const baseName1 = fileName1.split('/').pop();
  const baseName2 = fileName2.split('/').pop();

  return baseName1 === baseName2;
}

// 寻找最佳行匹配
function findBestLineMatch(line, allAIGeneratedContent) {
  const normalizedLine = normalizeCode(line);
  if (!normalizedLine) return null;

  // 先尝试精确匹配
  for (const content of allAIGeneratedContent) {
    if (!content.isBlock && normalizeCode(content.line) === normalizedLine) {
      return {
        recordId: content.recordId,
        similarity: 1.0
      };
    }
  }

  // 如果没有精确匹配，尝试相似度匹配
  let bestMatch = null;
  let highestSimilarity = 0.8; // 最低相似度阈值

  for (const content of allAIGeneratedContent) {
    if (!content.isBlock) {
      const similarity = calculateSimilarity(normalizedLine, normalizeCode(content.line));
      if (similarity > highestSimilarity) {
        highestSimilarity = similarity;
        bestMatch = {
          recordId: content.recordId,
          similarity
        };
      }
    }
  }

  return bestMatch;
}

// 对未匹配行进行块级比对
function performBlockBasedComparisonForUnmatched(unmatchedLines, allAIGeneratedContent) {
  const blockMatches = [];

  // 将未匹配行分组为连续块
  const lineBlocks = [];
  let currentBlock = [];

  for (let i = 0; i < unmatchedLines.length; i++) {
    const current = unmatchedLines[i];

    if (i === 0 || current.index === unmatchedLines[i-1].index + 1) {
      // 连续行，添加到当前块
      currentBlock.push(current);
    } else {
      // 不连续，开始新块
      if (currentBlock.length > 0) {
        lineBlocks.push([...currentBlock]);
      }
      currentBlock = [current];
    }
  }

  // 添加最后一个块
  if (currentBlock.length > 0) {
    lineBlocks.push(currentBlock);
  }

  // 对每个未匹配块进行比对
  for (const block of lineBlocks) {
    if (block.length < 3) continue; // 忽略太小的块

    const blockContent = block.map(item => item.content).join('\n');

    // 在AI生成内容中寻找匹配块
    for (const aiContent of allAIGeneratedContent) {
      if (aiContent.isBlock) {
        const similarity = calculateSimilarity(blockContent, aiContent.content);

        if (similarity >= 0.7) { // 块级匹配使用较低的阈值
          // 将匹配到的块的所有行都标记为匹配
          for (const lineItem of block) {
            blockMatches.push({
              type: '块级匹配',
              lineIndex: lineItem.index,
              aiRecordId: aiContent.recordId,
              similarity: similarity,
              blockSize: block.length
            });
          }
          break; // 找到匹配后退出内循环
        }
      }
    }
  }

  return blockMatches;
}

// 计算修改的行数
function calculateChangedLines(originalContent, currentContent) {
  if (!originalContent) return currentContent.split('\n').length; // 新文件所有行都是变更

  // 使用diff工具计算修改的行数
  try {
    const tempOrigFile = path.join(os.tmpdir(), 'orig_' + Date.now());
    const tempCurrFile = path.join(os.tmpdir(), 'curr_' + Date.now());

    fs.writeFileSync(tempOrigFile, originalContent);
    fs.writeFileSync(tempCurrFile, currentContent);

    const diffOutput = execSync(`diff -U0 "${tempOrigFile}" "${tempCurrFile}" | grep '^+[^+]' | wc -l`).toString();

    fs.unlinkSync(tempOrigFile);
    fs.unlinkSync(tempCurrFile);

    return parseInt(diffOutput.trim(), 10);
  } catch (error) {
    console.error('计算变更行数失败:', error);
    return 0;
  }
}

// 检查是否为代码文件
function isCodeFile(fileName) {
  const codeExtensions = [
    '.js', '.jsx', '.ts', '.tsx', '.vue', '.html', '.css', '.scss', '.less',
    '.java', '.py', '.php', '.go', '.rb', '.c', '.cpp', '.h', '.cs', '.swift'
  ];

  const ext = path.extname(fileName).toLowerCase();
  return codeExtensions.includes(ext);
}

// 从PR标题提取MIS号
function extractMisNumber(title) {
  if (!title) return null;

  // 常见的MIS号格式匹配
  const patterns = [
    /MIS[_-]?(\d+)/i,           // MIS_123456 或 MIS-123456
    /mis[_-]?(\d+)/i,           // mis_123456 或 mis-123456
    /(\d{6,})/,                 // 6位以上数字
    /[#](\d+)/,                 // #123456
    /ID[_-]?(\d+)/i,            // ID_123456
    /TASK[_-]?(\d+)/i           // TASK_123456
  ];

  for (const pattern of patterns) {
    const match = title.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return null;
}

// 生成最终报告
function generateFinalReport(analysisResults, prId, title, gitRepo, gitBranch) {
  // 计算总体统计数据
  let totalLines = 0;
  let totalChangedLines = 0;
  let totalAILines = 0;

  for (const result of analysisResults) {
    totalLines += result.totalLines;
    totalChangedLines += result.changedLines;
    totalAILines += result.matchedLines;
  }

  const overallAIRatio = totalChangedLines > 0 ? (totalAILines / totalChangedLines) : 0;

  // 构建最终结果
  return {
    pr_global_id: prId,
    pr_title: title,
    git_repository: gitRepo,
    git_branch: gitBranch,
    analysis_timestamp: new Date().toISOString(),
    mis_number: extractMisNumber(title) || 'N/A',  // 从PR标题提取MIS号
    detection_method: '行级+块级匹配',              // 检测方法
    summary: {
      total_files: analysisResults.length,
      total_lines: totalLines,
      changed_lines: totalChangedLines,
      ai_generated_lines: totalAILines,
      ai_code_ratio: overallAIRatio
    },
    file_details: analysisResults.map(result => ({
      file_name: result.fileName,
      file_type: result.fileType,
      total_lines: result.totalLines,
      changed_lines: result.changedLines,
      ai_matched_lines: result.matchedLines,
      ai_code_ratio: result.aiCodePortion,
      detection_method: '行级+块级匹配'             // 文件级检测方法
    }))
  };
}

// 打印最终报告到控制台
function printFinalReport(finalResult) {
  console.log('\n' + '='.repeat(80));
  console.log('🎯 AI代码分析最终报告');
  console.log('='.repeat(80));

  console.log('\n📋 基本信息:');
  console.log(`  PR编号: ${finalResult.pr_global_id}`);
  console.log(`  PR标题: ${finalResult.pr_title}`);
  console.log(`  仓库: ${finalResult.git_repository}`);
  console.log(`  分支: ${finalResult.git_branch}`);
  console.log(`  MIS号: ${finalResult.mis_number}`);
  console.log(`  检测方法: ${finalResult.detection_method}`);
  console.log(`  提交日期: ${finalResult.analysis_timestamp}`);

  console.log('\n📊 总体统计:');
  console.log(`  总文件数: ${finalResult.summary.total_files}`);
  console.log(`  总行数: ${finalResult.summary.total_lines}`);
  console.log(`  变更行数: ${finalResult.summary.changed_lines}`);
  console.log(`  AI生成行数: ${finalResult.summary.ai_generated_lines}`);
  console.log(`  AI代码占比: ${(finalResult.summary.ai_code_ratio * 100).toFixed(2)}%`);

  console.log('\n📁 文件详情:');
  finalResult.file_details.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.file_name}`);
    console.log(`     文件类型: ${file.file_type}`);
    console.log(`     总行数: ${file.total_lines}, 变更行数: ${file.changed_lines}`);
    console.log(`     AI匹配行数: ${file.ai_matched_lines}, AI占比: ${(file.ai_code_ratio * 100).toFixed(2)}%`);
    console.log(`     检测方法: ${file.detection_method}`);
  });

  console.log('\n' + '='.repeat(80));
  console.log('✅ 报告输出完成');
  console.log('='.repeat(80));

  console.log("上报结果：", JSON.stringify(finalResult, null, 2));
}

// 代码标准化函数
function normalizeCode(code) {
  if (!code) return '';

  return code
    .trim()
    .replace(/\s+/g, ' ')  // 将多个空白字符替换为单个空格
    .replace(/;\s*$/, '')  // 移除行尾分号
    .toLowerCase();        // 转为小写
}

// 计算相似度函数
function calculateSimilarity(str1, str2) {
  if (!str1 || !str2) return 0;
  if (str1 === str2) return 1;

  // 使用简单的编辑距离算法
  const len1 = str1.length;
  const len2 = str2.length;
  const matrix = [];

  // 初始化矩阵
  for (let i = 0; i <= len1; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j;
  }

  // 填充矩阵
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // 删除
          matrix[i][j - 1] + 1,     // 插入
          matrix[i - 1][j - 1] + 1  // 替换
        );
      }
    }
  }

  const editDistance = matrix[len1][len2];
  const maxLen = Math.max(len1, len2);
  return maxLen === 0 ? 1 : (maxLen - editDistance) / maxLen;
}

// 将代码分割为代码块
function splitIntoCodeBlocks(content) {
  if (!content) return [];

  const lines = content.split('\n');
  const blocks = [];
  let currentBlock = [];
  let startLine = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // 如果是空行或注释行，可能是块的分界
    if (!line || line.startsWith('//') || line.startsWith('/*') || line.startsWith('*')) {
      if (currentBlock.length >= 3) { // 至少3行才算一个块
        blocks.push({
          content: currentBlock.join('\n'),
          startLine: startLine,
          endLine: i - 1
        });
      }
      currentBlock = [];
      startLine = i + 1;
    } else {
      currentBlock.push(lines[i]);
    }
  }

  // 处理最后一个块
  if (currentBlock.length >= 3) {
    blocks.push({
      content: currentBlock.join('\n'),
      startLine: startLine,
      endLine: lines.length - 1
    });
  }

  return blocks;
}

AICodingAnalysis()