{"pr_global_id": "13073502", "pr_title": "【不要合并，只是测试webhook】222222", "git_repository": "ssh://*******************/dzfe/medical-home-page.git", "git_branch": "feature/fedo-213908", "analysis_timestamp": "2025-05-24T05:15:56.314Z", "summary": {"total_files": 11, "total_lines": 1018, "changed_lines": 1018, "ai_generated_lines": 0, "ai_code_ratio": 0}, "file_details": [{"file_name": "build/talos-ai-coding-analysis.es.js", "file_type": "js", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "build/talos-ai-coding-analysis.umd.js", "file_type": "js", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "dist/talos-ai-coding-analysis.es.js", "file_type": "js", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "dist/talos-ai-coding-analysis.umd.js", "file_type": "js", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "doc.config.js", "file_type": "js", "total_lines": 102, "changed_lines": 102, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "examples/index.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "examples/style.css", "file_type": "css", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "index.html", "file_type": "html", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "scripts/fix-vite.js", "file_type": "js", "total_lines": 64, "changed_lines": 64, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/index.js", "file_type": "js", "total_lines": 760, "changed_lines": 760, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "vite.config.js", "file_type": "js", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}]}